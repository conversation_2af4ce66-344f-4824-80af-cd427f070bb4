{% load i18n %}
{% load matter_forms %}

<div id="student-form">
    {% csrf_token %}
    
    <div class="student-form-columns">
        <!-- Column 1: Student Information -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">person</span>
                Informations sur l'élève
            </div>
            
            <!-- Student ID -->
             <div class="row">
                 <div class="form-field">
                     {% matter_field student_form.student_id class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                 </div>
             </div>
            <!-- Name Fields -->
            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% matter_field student_form.last_name class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                </div>
                <div class="form-field col-md-8">
                    {% if user.school.education != 'F' %}
                        {% matter_field student_form.first_name class="small" onkeyup="this.value = this.value.toUpperCase();" hx_get="/transliterate/?field=full_name_ar" hx_target="#id_0-full_name_ar_container" hx_trigger="keyup delay:3s" hx_swap="outerHTML" %}
                    {% else %}
                        {% matter_field student_form.first_name class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                    {% endif %}
                </div>
            </div>
            
            <!-- Birth Date Fields -->
            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% matter_field student_form.birth_day class="small" type="number" min="1" max="31" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.birth_month class="small" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.birth_year class="small" type="number" max="{{ max_year }}" %}
                </div>
            </div>
            
            <!-- Birth Place, Gender, Nationality -->
            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% if user.school.education != 'F' %}
                        {% matter_field student_form.birth_place class="small" onkeyup="this.value = this.value.toUpperCase();" hx_get="/transliterate/?field=birth_place_ar" hx_target="#id_0-birth_place_ar_container" hx_trigger="keyup delay:3s" hx_swap="outerHTML" %}
                    {% else %}
                        {% matter_field student_form.birth_place class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                    {% endif %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.gender class="small" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.nationality class="small" %}
                </div>
            </div>

            <!-- Arabic Name Fields (if not French education) -->
            {% if user.school.education != 'F' %}
            <div class="row gx-2">
                <div class="form-field col-md-6">
                    {% matter_field student_form.full_name_ar class='small' %}
                </div>
                <div class="form-field col-md-6">
                    {% matter_field student_form.birth_place_ar class='small' %}
                </div>
            </div>
            {% endif %}
        </div>
        
        <!-- Column 2: Parent Information and Photo -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">family_restroom</span>
                Infos parents et Photo
            </div>
            
            <!-- Father -->
            <div class="form-field">
                {% matter_field parents_form.father class='small' onkeyup="this.value = this.value.toUpperCase();" %}
            </div>

            <!-- Mother -->
            <div class="form-field">
                {% matter_field parents_form.mother class='small' onkeyup="this.value = this.value.toUpperCase();" %}
            </div>

            <!-- Parent Phone -->
            <div class="form-field">
                {% matter_field parents_form.father_phone class='small' type="tel" %}
            </div>
            
            <!-- Photo Upload Section -->
            <div class="photo-upload-section">
                {% if enrollment and enrollment.student.photo %}
                    <img alt="Photo de l'élève" class="photo-preview" 
                         src="{{ enrollment.student.photo.url }}" id="photo-preview">
                {% else %}
                    <img alt="Photo de l'élève" class="photo-preview" 
                         src="{{ blank_photo }}" id="photo-preview">
                {% endif %}
                
                <input type="file" class="mdc-text-field__input" 
                       name="3-photo" id="{{ files_form.photo.id_for_label }}"
                       accept=".jpg, .png, .jpeg" 
                       onchange="previewPhoto(this)"
                       style="display: none;">
                
                <button type="button" class="mdc-button mdc-button--outlined" 
                        onclick="document.getElementById('{{ files_form.photo.id_for_label }}').click()">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons mdc-button__icon">photo_camera</span>
                    <span class="mdc-button__label">Choisir une photo</span>
                </button>
            </div>
        </div>
        
        <!-- Column 3: Registration Information -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">school</span>
                Fréquentation {{ active_year }}
            </div>

            <!-- Subschool (if multiple) -->
            {% if user.school.subschool_set.count > 1 %}
            <div class="form-field">
                {% matter_field level_form.subschool class='small' %}
            </div>
            {% endif %}

            <!-- Level Fields -->
            <div class="row gx-2">
                {% if level_form.level_fr.help_text != 'd-none' %}
                <div class="form-field col-md-6">
                    {% matter_field level_form.level_fr class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                    {% matter_field level_form.generic_level_fr class='small d-none' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                </div>
                {% endif %}

                {% if level_form.level_ar.help_text != 'd-none' %}
                <div class="form-field col-md-6">
                    {% matter_field level_form.level_ar class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                    {% matter_field level_form.generic_level_ar class='small d-none' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                </div>
                {% endif %}
            </div>

            <!-- Quality and Status -->
            <div class="row gx-2">
                <div class="form-field col-md-6" style="flex: 2;">
                    {% matter_field level_form.qualite class='small' %}
                </div>
                <div class="form-field col-md-6" style="flex: 1;">
                    {% matter_field level_form.status class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                </div>
            </div>

            <!-- Fees Section -->
            {% if perms.school.add_payment %}

            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% matter_field level_form.enrollment_fees class='small' type="number" min="0" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field level_form.year_fees class='small' type="number" min="0" hx_swap="outerHTML" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field level_form.annexe_fees class='small' type="number" min="0" %}
                </div>
            </div>

            <!-- Compact Payment Section -->
            <div class="form-section-title" style="margin-top: 8px;">
                <span class="material-icons">receipt_long</span>
                Premier versement (optionnel)
            </div>

            <div class="row gx-2">
                <div class="form-field col-md-3">
                    {% matter_field level_form.enrollment_fee1 class='extra-small' type="number" min="0" placeholder="0" %}
                </div>
                <div class="form-field col-md-3">
                    {% matter_field level_form.year_fee1 class='extra-small' type="number" min="0" placeholder="0" %}
                </div>
                <div class="form-field col-md-3">
                    {% matter_field level_form.annexe_fee1 class='extra-small' type="number" min="0" placeholder="0" %}
                </div>
                <div class="form-field col-md-3">
                    {% matter_field level_form.date1 class='extra-small' type="date" %}
                </div>
            </div>

            <!-- Payment Summary -->
            <div class="payment-summary" id="payment-summary" style="display: none; margin-top: 12px;">
                <div class="summary-card">
                    <div class="summary-header">
                        <span class="material-icons">calculate</span>
                        <span>Total: </span>
                        <span id="payment-total">0 F CFA</span>
                    </div>
                </div>
            </div>

            <!-- SMS Notification -->
            <div class="sms-notification" style="margin-top: 8px;">
                <div class="mdc-form-field">
                    <div class="mdc-checkbox">
                        <input type="checkbox"
                               class="mdc-checkbox__native-control"
                               name="sms_check"
                               id="sms_check"
                               checked
                               {% if not sms_balance or sms_balance == 0 %}disabled{% endif %}>
                        <div class="mdc-checkbox__background">
                            <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                <path class="mdc-checkbox__checkmark-path"
                                      fill="none"
                                      d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                            </svg>
                            <div class="mdc-checkbox__mixedmark"></div>
                        </div>
                        <div class="mdc-checkbox__ripple"></div>
                    </div>
                    <label for="sms_check" class="mdc-checkbox-label">Notification SMS</label>
                </div>

                <div class="sms-balance {% if sms_balance and sms_balance > 0 %}positive{% else %}zero{% endif %}">
                    <span class="material-icons">account_balance_wallet</span>
                    <span>Solde: {{ sms_balance|default:'0' }}</span>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
/* Form Section Titles */
.form-section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #1976d2;
    margin: 0 0 12px 0;
    font-size: 16px;
}

.form-section-title .material-icons {
    font-size: 20px;
}

/* Compact Payment Summary */
.payment-summary {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 100px;
    }
}

.summary-card {
    background: linear-gradient(135deg, #4caf50, #45a049);
    color: white;
    border-radius: 8px;
    padding: 12px 16px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.summary-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
}

.summary-header .material-icons {
    font-size: 18px;
}

/* SMS Notification */
.sms-notification {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px 16px;
    gap: 16px;
}

.sms-notification .mdc-form-field {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sms-notification .mdc-checkbox {
    --mdc-theme-secondary: #1976d2;
}

.sms-notification .mdc-checkbox-label {
    font-size: 13px;
    color: #333;
    font-weight: 500;
}

.sms-balance {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.sms-balance.positive {
    background: #e8f5e8;
    color: #2e7d32;
}

.sms-balance.zero {
    background: #f5f5f5;
    color: #666;
}

.sms-balance .material-icons {
    font-size: 16px;
}

/* Responsive */
@media (max-width: 768px) {
    .sms-notification {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .sms-balance {
        justify-content: center;
    }
}
</style>

<script>
function previewPhoto(input) {
    const preview = document.getElementById('photo-preview');
    const reader = new FileReader();

    if (input.files && input.files[0]) {
        reader.onload = function(e) {
            preview.src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.src = '{{ blank_photo }}';
    }
}

// Payment calculation
function updatePaymentTotal() {
    const inscriptionInput = document.querySelector('[name="2-enrollment_fee1"]');
    const scolariteInput = document.querySelector('[name="2-year_fee1"]');
    const annexeInput = document.querySelector('[name="2-annexe_fee1"]');

    if (!inscriptionInput || !scolariteInput || !annexeInput) return;

    const inscription = parseFloat(inscriptionInput.value) || 0;
    const scolarite = parseFloat(scolariteInput.value) || 0;
    const annexe = parseFloat(annexeInput.value) || 0;

    const total = inscription + scolarite + annexe;
    const totalElement = document.getElementById('payment-total');
    const summaryElement = document.getElementById('payment-summary');

    if (totalElement) {
        totalElement.textContent = total.toLocaleString() + ' F CFA';
    }

    if (summaryElement) {
        summaryElement.style.display = total > 0 ? 'block' : 'none';
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Add payment calculation listeners
    const paymentInputs = document.querySelectorAll('[name="2-enrollment_fee1"], [name="2-year_fee1"], [name="2-annexe_fee1"]');
    paymentInputs.forEach(input => {
        input.addEventListener('input', updatePaymentTotal);
    });

    // Set default date to today
    const dateInput = document.querySelector('[name="2-date1"]');
    if (dateInput && !dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }

    // Initialize MDC checkbox
    const checkboxEl = document.querySelector('.mdc-checkbox');
    if (checkboxEl && typeof mdc !== 'undefined') {
        const checkbox = new mdc.checkbox.MDCCheckbox(checkboxEl);
    }
});
</script>
