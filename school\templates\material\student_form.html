{% load i18n %}
{% load matter_forms %}

<div id="student-form">
    {% csrf_token %}
    
    <div class="student-form-columns">
        <!-- Column 1: Student Information -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">person</span>
                Informations sur l'élève
            </div>
            
            <!-- Student ID -->
             <div class="row">
                 <div class="form-field">
                     {% matter_field student_form.student_id class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                 </div>
             </div>
            <!-- Name Fields -->
            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% matter_field student_form.last_name class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                </div>
                <div class="form-field col-md-8">
                    {% if user.school.education != 'F' %}
                        {% matter_field student_form.first_name class="small" onkeyup="this.value = this.value.toUpperCase();" hx_get="/transliterate/?field=full_name_ar" hx_target="#id_0-full_name_ar_container" hx_trigger="keyup delay:3s" hx_swap="outerHTML" %}
                    {% else %}
                        {% matter_field student_form.first_name class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                    {% endif %}
                </div>
            </div>
            
            <!-- Birth Date Fields -->
            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% matter_field student_form.birth_day class="small" type="number" min="1" max="31" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.birth_month class="small" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.birth_year class="small" type="number" max="{{ max_year }}" %}
                </div>
            </div>
            
            <!-- Birth Place, Gender, Nationality -->
            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% if user.school.education != 'F' %}
                        {% matter_field student_form.birth_place class="small" onkeyup="this.value = this.value.toUpperCase();" hx_get="/transliterate/?field=birth_place_ar" hx_target="#id_0-birth_place_ar_container" hx_trigger="keyup delay:3s" hx_swap="outerHTML" %}
                    {% else %}
                        {% matter_field student_form.birth_place class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                    {% endif %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.gender class="small" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field student_form.nationality class="small" %}
                </div>
            </div>

            <!-- Arabic Name Fields (if not French education) -->
            {% if user.school.education != 'F' %}
            <div class="row gx-2">
                <div class="form-field col-md-6">
                    {% matter_field student_form.full_name_ar class='small' %}
                </div>
                <div class="form-field col-md-6">
                    {% matter_field student_form.birth_place_ar class='small' %}
                </div>
            </div>
            {% endif %}
        </div>
        
        <!-- Column 2: Parent Information and Photo -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">family_restroom</span>
                Infos parents et Photo
            </div>
            
            <!-- Father -->
            <div class="form-field">
                {% matter_field parents_form.father class='small' onkeyup="this.value = this.value.toUpperCase();" %}
            </div>

            <!-- Mother -->
            <div class="form-field">
                {% matter_field parents_form.mother class='small' onkeyup="this.value = this.value.toUpperCase();" %}
            </div>

            <!-- Parent Phone -->
            <div class="form-field">
                {% matter_field parents_form.father_phone class='small' type="tel" %}
            </div>
            
            <!-- Photo Upload Section -->
            <div class="photo-upload-section">
                {% if enrollment and enrollment.student.photo %}
                    <img alt="Photo de l'élève" class="photo-preview" 
                         src="{{ enrollment.student.photo.url }}" id="photo-preview">
                {% else %}
                    <img alt="Photo de l'élève" class="photo-preview" 
                         src="{{ blank_photo }}" id="photo-preview">
                {% endif %}
                
                <input type="file" class="mdc-text-field__input" 
                       name="3-photo" id="{{ files_form.photo.id_for_label }}"
                       accept=".jpg, .png, .jpeg" 
                       onchange="previewPhoto(this)"
                       style="display: none;">
                
                <button type="button" class="mdc-button mdc-button--outlined" 
                        onclick="document.getElementById('{{ files_form.photo.id_for_label }}').click()">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons mdc-button__icon">photo_camera</span>
                    <span class="mdc-button__label">Choisir une photo</span>
                </button>
            </div>
        </div>
        
        <!-- Column 3: Registration Information -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">school</span>
                Fréquentation {{ active_year }}
            </div>

            <!-- Subschool (if multiple) -->
            {% if user.school.subschool_set.count > 1 %}
            <div class="form-field">
                {% matter_field level_form.subschool class='small' %}
            </div>
            {% endif %}

            <!-- Level Fields -->
            <div class="row gx-2">
                {% if level_form.level_fr.help_text != 'd-none' %}
                <div class="form-field col-md-6">
                    {% matter_field level_form.level_fr class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                    {% matter_field level_form.generic_level_fr class='small d-none' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                </div>
                {% endif %}

                {% if level_form.level_ar.help_text != 'd-none' %}
                <div class="form-field col-md-6">
                    {% matter_field level_form.level_ar class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                    {% matter_field level_form.generic_level_ar class='small d-none' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                </div>
                {% endif %}
            </div>

            <!-- Quality and Status -->
            <div class="row gx-2">
                <div class="form-field col-md-6" style="flex: 2;">
                    {% matter_field level_form.qualite class='small' %}
                </div>
                <div class="form-field col-md-6" style="flex: 1;">
                    {% matter_field level_form.status class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                </div>
            </div>

            <!-- Fees Section -->
            {% if perms.school.add_payment %}
            <div class="form-section-title" style="margin-top: 24px;">
                <span class="material-icons">payments</span>
                Montant à payer par rubrique
            </div>

            <div class="row gx-2">
                <div class="form-field col-md-4">
                    {% matter_field level_form.enrollment_fees class='small' type="number" min="0" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field level_form.year_fees class='small' type="number" min="0" hx_swap="outerHTML" %}
                </div>
                <div class="form-field col-md-4">
                    {% matter_field level_form.annexe_fees class='small' type="number" min="0" %}
                </div>
            </div>

            <!-- Payment Section -->
            <div class="payment-section" style="margin-top: 24px;">
                <div class="payment-section-header" onclick="togglePaymentSection()">
                    <div class="form-section-title">
                        <span class="material-icons">receipt_long</span>
                        Premier versement (optionnel)
                    </div>
                    <span class="material-icons payment-toggle-icon">expand_more</span>
                </div>

                <div class="payment-section-content" id="payment-content" style="display: none;">
                    {% include 'material/payment_component.html' with form=level_form hide_agent=True %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.payment-section {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.payment-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: #f8f9fa;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.payment-section-header:hover {
    background: #e9ecef;
}

.payment-toggle-icon {
    transition: transform 0.3s ease;
    color: #666;
}

.payment-section-header.expanded .payment-toggle-icon {
    transform: rotate(180deg);
}

.payment-section-content {
    border-top: 1px solid #e0e0e0;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 1000px;
    }
}

.form-section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #1976d2;
    margin: 0;
}

.form-section-title .material-icons {
    font-size: 20px;
}
</style>

<script>
function previewPhoto(input) {
    const preview = document.getElementById('photo-preview');
    const reader = new FileReader();

    if (input.files && input.files[0]) {
        reader.onload = function(e) {
            preview.src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.src = '{{ blank_photo }}';
    }
}

function togglePaymentSection() {
    const content = document.getElementById('payment-content');
    const header = document.querySelector('.payment-section-header');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        header.classList.add('expanded');
    } else {
        content.style.display = 'none';
        header.classList.remove('expanded');
    }
}

// Auto-expand payment section if there are existing payments or if any payment field has a value
document.addEventListener('DOMContentLoaded', function() {
    const paymentInputs = document.querySelectorAll('[name="2-enrollment_fee1"], [name="2-year_fee1"], [name="2-annexe_fee1"]');
    const hasExistingPayments = document.querySelector('.existing-payments');

    let hasValues = false;
    paymentInputs.forEach(input => {
        if (input && input.value && parseFloat(input.value) > 0) {
            hasValues = true;
        }
    });

    if (hasExistingPayments || hasValues) {
        togglePaymentSection();
    }
});
</script>
