{% load matter_forms %}

<div class="payment-component">
    <!-- Existing Payments Table (if any) -->
    {% if enrollment and enrollment.payment_set.exists %}
    <div class="existing-payments">
        <h6 class="payment-subtitle">
            <span class="material-icons">history</span>
            Versements existants
        </h6>
        
        <div class="payments-table-container">
            <table class="payments-table">
                <thead>
                    <tr>
                        <th>Inscription</th>
                        <th>Scolarité</th>
                        <th>Annexe</th>
                        <th>Date</th>
                        {% if not hide_agent %}
                        <th>Agent</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% for payment in payments %}
                    <tr>
                        <td>{{ payment.inscription|default:"0" }} F</td>
                        <td>{{ payment.amount|default:"0" }} F</td>
                        <td>{{ payment.annexe|default:"0" }} F</td>
                        <td>{{ payment.date_str }}</td>
                        {% if not hide_agent %}
                        <td>{{ payment.agent.get_full_name|default:"—" }}</td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="totals-row">
                        <td><strong id="total_inscription">{{ enrollment.get_inscription_payments_total }} F</strong></td>
                        <td><strong id="total_amount">{{ enrollment.get_scolarite_payments_total }} F</strong></td>
                        <td><strong id="total_annexe">{{ enrollment.get_annexe_payments_total }} F</strong></td>
                        <td colspan="{% if not hide_agent %}2{% else %}1{% endif %}">
                            <strong id="big_total">{{ enrollment.get_payments_total }} F</strong>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- New Payment Form -->
    <div class="new-payment-form">
        <h6 class="payment-subtitle">
            <span class="material-icons">add_circle</span>
            Nouveau versement
        </h6>
        
        <div class="payment-fields">
            <div class="payment-amounts">
                <div class="row gx-2">
                    <div class="form-field col-md-4">
                        {% matter_field form.enrollment_fee1 class='small' type="number" min="0" placeholder="0" %}
                    </div>
                    <div class="form-field col-md-4">
                        {% matter_field form.year_fee1 class='small' type="number" min="0" placeholder="0" %}
                    </div>
                    <div class="form-field col-md-4">
                        {% matter_field form.annexe_fee1 class='small' type="number" min="0" placeholder="0" %}
                    </div>
                </div>
            </div>

            <div class="payment-date">
                <div class="row">
                    <div class="form-field col-md-6">
                        {% matter_field form.date1 class='small' type="date" %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Summary -->
        <div class="payment-summary" id="payment-summary" style="display: none;">
            <div class="summary-card">
                <div class="summary-header">
                    <span class="material-icons">calculate</span>
                    <span>Total du versement</span>
                </div>
                <div class="summary-amount" id="new-payment-total">0 F CFA</div>
            </div>
        </div>

        <!-- SMS Notification -->
        <div class="sms-notification">
            <label class="mdc-checkbox-wrapper">
                <input type="checkbox" name="sms_check" id="sms_check" class="mdc-checkbox-input" checked
                       {% if not sms_balance or sms_balance == 0 %}disabled{% endif %}>
                <div class="mdc-checkbox">
                    <span class="material-icons">sms</span>
                </div>
                <span class="mdc-checkbox-label">Notification SMS après versement</span>
            </label>
            
            <div class="sms-balance {% if sms_balance and sms_balance > 0 %}balance-positive{% else %}balance-zero{% endif %}">
                <span class="material-icons">account_balance_wallet</span>
                Solde SMS: {{ sms_balance|default:'0' }}
            </div>
        </div>
    </div>
</div>

<style>
.payment-component {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.payment-subtitle {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 10px;
    color: #1976d2;
    font-weight: 500;
    font-size: 14px;
}

.payment-subtitle .material-icons {
    font-size: 18px;
}

.payments-table-container {
    background: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 12px;
}

.payments-table {
    width: 100%;
    border-collapse: collapse;
}

.payments-table th,
.payments-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    font-size: 13px;
}

.payments-table th {
    background: #1976d2;
    color: white;
    font-weight: 500;
    font-size: 13px;
}

.payments-table tbody tr:hover {
    background: #f5f5f5;
}

.totals-row {
    background: #e3f2fd !important;
    font-weight: 500;
}

.payment-fields {
    background: white;
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 10px;
}

.payment-amounts {
    margin-bottom: 10px;
}

.payment-date {
    border-top: 1px solid #f0f0f0;
    padding-top: 10px;
}

.payment-summary {
    margin: 10px 0;
}

.summary-card {
    background: linear-gradient(135deg, #4caf50, #45a049);
    color: white;
    border-radius: 6px;
    padding: 10px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.summary-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    margin-bottom: 6px;
    font-size: 12px;
    opacity: 0.9;
}

.summary-amount {
    font-size: 18px;
    font-weight: bold;
}

.sms-notification {
    background: white;
    border-radius: 6px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.mdc-checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    cursor: pointer;
}

.mdc-checkbox-input {
    display: none;
}

.mdc-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid #1976d2;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.mdc-checkbox-input:checked + .mdc-checkbox {
    background: #1976d2;
    color: white;
}

.mdc-checkbox-input:disabled + .mdc-checkbox {
    border-color: #ccc;
    background: #f5f5f5;
}

.mdc-checkbox .material-icons {
    font-size: 16px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.mdc-checkbox-input:checked + .mdc-checkbox .material-icons {
    opacity: 1;
}

.mdc-checkbox-label {
    font-size: 13px;
    color: #333;
}

.sms-balance {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.balance-positive {
    background: #e8f5e8;
    color: #2e7d32;
}

.balance-zero {
    background: #f5f5f5;
    color: #666;
}

.sms-balance .material-icons {
    font-size: 16px;
}

/* Responsive */
@media (max-width: 768px) {
    .payment-component {
        padding: 10px;
    }

    .payments-table th,
    .payments-table td {
        padding: 6px;
        font-size: 11px;
    }

    .summary-amount {
        font-size: 16px;
    }

    .payment-subtitle {
        font-size: 13px;
    }

    .mdc-checkbox-label {
        font-size: 12px;
    }
}
</style>

<script>
// Payment calculation
function updatePaymentTotal() {
    const inscriptionInput = document.querySelector('[name="2-enrollment_fee1"]');
    const scolariteInput = document.querySelector('[name="2-year_fee1"]');
    const annexeInput = document.querySelector('[name="2-annexe_fee1"]');
    
    if (!inscriptionInput || !scolariteInput || !annexeInput) return;
    
    const inscription = parseFloat(inscriptionInput.value) || 0;
    const scolarite = parseFloat(scolariteInput.value) || 0;
    const annexe = parseFloat(annexeInput.value) || 0;
    
    const total = inscription + scolarite + annexe;
    const totalElement = document.getElementById('new-payment-total');
    const summaryElement = document.getElementById('payment-summary');
    
    if (totalElement) {
        totalElement.textContent = total.toLocaleString() + ' F CFA';
    }
    
    if (summaryElement) {
        summaryElement.style.display = total > 0 ? 'block' : 'none';
    }
}

// Add event listeners when the component loads
document.addEventListener('DOMContentLoaded', function() {
    const paymentInputs = document.querySelectorAll('[name="2-enrollment_fee1"], [name="2-year_fee1"], [name="2-annexe_fee1"]');
    paymentInputs.forEach(input => {
        input.addEventListener('input', updatePaymentTotal);
    });
    
    // Set default date to today
    const dateInput = document.querySelector('[name="2-date1"]');
    if (dateInput && !dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }
});
</script>
