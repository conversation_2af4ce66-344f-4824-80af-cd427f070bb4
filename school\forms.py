import json
from collections.abc import Mapping
from datetime import date
from typing import Any, Dict
from django import forms
from django.forms import Form
from django.core.files.base import File
from django.db.models.base import Model
from django.forms.utils import ErrorList
from django.urls import reverse
from django.db.models import Q, F, Count
from django.utils import timezone
from . import models
from main import utils
from exams.models import LevelSubject

class StudentFormStepOneBasicInfos(forms.ModelForm):
    def __init__(self, user, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user.school.education == utils.EDUCATION_FRENCH:
            del self.fields['full_name_ar']
            del self.fields['birth_place_ar']
        self.fields['last_name'].widget.attrs['hx-include'] = \
            "[name='first_name']"
        self.fields['first_name'].widget.attrs['hx-include'] = \
            "[name='last_name']"

    class Meta:
        model = models.Student
        fields = [
            'student_id', 'last_name', 'first_name', 'birth_day', 
            'birth_month', 'birth_year', 'gender', 'nationality', 'birth_place', 
            'full_name_ar', 'birth_place_ar', 
        ]


class StudentFormStepTwoParents(forms.ModelForm):
    class Meta:
        model = models.Student
        fields = [
            'father', 'father_phone', 'mother', 'mother_phone',
        ]


class StudentFormStepFourPhotos(forms.ModelForm):
    prefix = '3'
    class Meta:
        model = models.Student
        fields = ['photo', 'certificate_img']


def filter_levels_to_display(pk, form, user, year, kwargs):
    """Filter levels to display based on user permissions 
        and if the form is in create mode and if student has grades"""
    qs_fr = models.Level.objects.for_user(user=user, year=year)
    qs_ar = models.Level.objects.for_user(user=user, education=utils.EDUCATION_ARABIC, year=year)
    form.fields['level_fr'].queryset = qs_fr
    form.fields['level_ar'].queryset = qs_ar
    if not kwargs.get('instance'):
        for field in form.fields:
            form.fields[field].widget.attrs['hx-vals'] = json.dumps({'create_mode': '1'})

class StudentFormStepThreePayment(forms.ModelForm):
    prefix = '2'
    enrollment_fee1 = forms.IntegerField(
        initial=0, required=False, label="Inscription")
    year_fee1 = forms.IntegerField(
        initial=0, required=False, label="Scolarité")
    annexe_fee1 = forms.IntegerField(
        initial=0, required=False, label="Annexe")
    date1 = forms.DateTimeField(initial=timezone.now, required=False, label="Date")
    sms_check = forms.BooleanField(initial=True, required=False, label="Notification SMS")
    
    def get_initial_for_field(self, field, field_name):
        if field_name.startswith('level_'):
            enrollment = self.instance
            if enrollment and field_name == 'level_fr' and enrollment.generic_level_fr and not enrollment.level_fr:
                qs_fr = models.Level.objects.for_user(user=self.user, year=self.year)
                return qs_fr.filter(generic_level=enrollment.generic_level_fr).first()
            elif enrollment and field_name == 'level_ar' and enrollment.generic_level_ar and not enrollment.level_ar:
                qs_ar = models.Level.objects.for_user(user=self.user, education=utils.EDUCATION_ARABIC, year=self.year)
                return qs_ar.filter(generic_level=enrollment.generic_level_ar).first()
        return super().get_initial_for_field(field, field_name)
    
    def clean_level_fr(self):
        level_fr = self.cleaned_data['level_fr']
        if self.instance.pk is not None and not (self.instance.level_fr == level_fr) and level_fr.enrollment_set.count() >= level_fr.max:
            raise forms.ValidationError(f"Effectif maxi atteint: {level_fr.max}")
        return level_fr
    
    def clean_level_ar(self):
        level_ar = self.cleaned_data['level_ar']
        if self.instance.pk is not None and level_ar and \
            self.instance.level_ar and not (self.instance.level_ar == level_ar) and \
                level_ar.enrollment_set.count() >= level_ar.max:
            raise forms.ValidationError(f"Effectif maxi atteint: {level_ar.max}")
        return level_ar
    
    def clean_generic_level_fr(self):
        generic_level_fr = self.cleaned_data['generic_level_fr']
        if self.instance.pk is not None and self.instance.grade_set.exists():
            if self.instance.grade_set.filter(grade__isnull=False, grade__gt=0, school_term__education=utils.EDUCATION_FRENCH).exists() and \
                self.instance.generic_level_fr != generic_level_fr:
                raise forms.ValidationError("Impossible de changer de niveau car l'élève possède déjà des notes")
        return generic_level_fr
    
    def clean_generic_level_ar(self):
        generic_level_ar = self.cleaned_data['generic_level_ar']
        if self.instance.pk is not None and \
            generic_level_ar and self.instance.grade_set.exists() and\
                self.instance.generic_level_ar:
            if self.instance.grade_set.filter(grade__isnull=False, grade__gt=0, school_term__education=utils.EDUCATION_ARABIC).exists() and \
                self.instance.generic_level_ar != generic_level_ar:
                raise forms.ValidationError("Impossible de changer de niveau arabe car l'élève possède déjà des notes")
        return generic_level_ar

    def __init__(self, user, year, pk=None, *args, **kwargs):
        self.user = user
        self.year = year
        super().__init__(*args, **kwargs)
        self.fields['generic_level_ar'].widget.attrs['hx-include'] = \
            "[name='2-status'], [name='2-level_fr'], [name='2-level_ar'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees'],  [name='2-generic_level_fr']"
        self.fields['generic_level_fr'].widget.attrs['hx-include'] = \
            "[name='2-status'], [name='2-level_fr'], [name='2-level_ar'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees'],  [name='2-generic_level_ar']"
        self.fields['level_ar'].widget.attrs['hx-include'] = \
            "[name='2-status'], [name='2-level_fr'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees'], [name='2-generic_level_fr'], [name='2-generic_level_ar']"
        self.fields['level_fr'].widget.attrs['hx-include'] = \
            "[name='2-status'], [name='2-level_ar'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees'], [name='2-generic_level_fr'], [name='2-generic_level_ar']"
        self.fields['status'].widget.attrs['hx-include'] = \
            "[name='2-level_fr'], [name='2-level_ar'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees'], [name='2-generic_level_fr'], [name='2-generic_level_ar']"

        school = user.school
        if school.subschool_set.count() > 0:
            self.fields['subschool'].initial = school.subschool_set.filter(is_main_school=True).first()

        filter_levels_to_display(pk, self, user, year, kwargs)

        if not user.has_perm('school.add_payment'):
            self.fields['enrollment_fees'].widget.attrs['readOnly'] = True
            self.fields['year_fees'].widget.attrs['readOnly'] = True
            self.fields['annexe_fees'].widget.attrs['readOnly'] = True
            self.fields['enrollment_fee1'].widget.attrs['readOnly'] = True
            self.fields['year_fee1'].widget.attrs['readOnly'] = True
            self.fields['annexe_fee1'].widget.attrs['readOnly'] = True

    class Meta:
        model = models.Enrollment
        fields = [
            'generic_level_fr',  'generic_level_ar', 
            'level_fr', 'level_ar', 
            'status', 'qualite',
            'enrollment_fees', 'year_fees',
            'annexe_fees', 'subschool'
        ]


class StudentFormCustomStepOne(forms.ModelForm):
    def __init__(self, user, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user.school.education == utils.EDUCATION_FRENCH:
            field_names = ['full_name_ar', 'birth_place_ar', 'level_ar']
            for field_name in field_names:
                if field_name in self.fields:
                    del self.fields[field_name]
            # del self.fields['generic_level_ar']
        else:
            self.fields['first_name'].widget.attrs['hx-include'] = "[name='0-last_name']"
            self.fields['first_name'].widget.attrs['hx-get'] = '/transliterate/?field=full_name_ar'
            self.fields['first_name'].widget.attrs['hx-target'] = '#id_0-full_name_ar'
            self.fields['first_name'].widget.attrs['hx-trigger'] = 'keyup delay:3s'
            self.fields['first_name'].widget.attrs['hx-swap'] = 'outerHTML'
            
            self.fields['birth_place'].widget.attrs['hx-get'] = '/transliterate/?field=birth_place_ar'
            self.fields['birth_place'].widget.attrs['hx-target'] = '#id_0-birth_place_ar'
            self.fields['birth_place'].widget.attrs['hx-trigger'] = 'keyup delay:3s'
            self.fields['birth_place'].widget.attrs['hx-swap'] = 'outerHTML'

    def clean(self) -> Dict[str, Any]:
        # Ensure date is correct by constructing a date object
        birth_day = self.cleaned_data['birth_day']
        birth_month = self.cleaned_data['birth_month']
        birth_year = self.cleaned_data['birth_year']

        if birth_day or birth_month or birth_year:
            try:
                birth_date = date(birth_year, birth_month, birth_day)
            except:
                msg = 'Date invalide'
                raise forms.ValidationError(msg) 
        return self.cleaned_data

    class Meta:
        model = models.Student
        fields = [
            'student_id', 'last_name', 'first_name', 
            'gender', 'nationality',
            'birth_day','birth_month', 'birth_year', 
            'birth_place', 'father_phone', 
            'full_name_ar', 'birth_place_ar', 
        ]


class StudentFormCustomStepTwo(forms.ModelForm):
    prefix = '2'
    photo = forms.ImageField(required=False, label="Photo de l'élève")
    father = forms.CharField(max_length=255, label='Père', required=False)
    mother = forms.CharField(max_length=255, label='Mère', required=False)
    enrollment_fee1 = forms.IntegerField(
        initial=0, required=False, label="Vers. inscription 1")
    year_fee1 = forms.IntegerField(
        initial=0, required=False, label="Vers. scolarité 1")
    annexe_fee1 = forms.IntegerField(
        initial=0, required=False, label="Vers. annexe 1")
    
    class Meta:
        model = models.Enrollment
        fields = [
            'photo', 'father', 'mother',
            'level_fr', 'level_ar',
            'qualite', 'status',
            'enrollment_fees', 'year_fees', 'annexe_fees',
            'enrollment_fee1', 'year_fee1', 'annexe_fee1',
        ]

    def clean_level_fr(self):
        level_fr = self.cleaned_data['level_fr']
        if self.instance.pk is not None:
            if self.instance.grade_set.exists():
                if self.instance.grade_set.filter(grade__isnull=False, grade__gt=0, school_term__education=utils.EDUCATION_FRENCH).exists() and \
                    self.instance.level_fr.generic_level != level_fr.generic_level:
                    raise forms.ValidationError("Impossible de changer de niveau car l'élève possède déjà des notes")
            elif not (self.instance.level_fr == level_fr) and level_fr.enrollment_set.count() >= level_fr.max:
                raise forms.ValidationError(f"Effectif maxi atteint: {level_fr.max}")
        return level_fr
    
    def clean_level_ar(self):
        level_ar = self.cleaned_data['level_ar']
        if self.instance.pk is not None and level_ar:
            if self.instance.grade_set.exists():
                if self.instance.level_ar and self.instance.grade_set.filter(grade__isnull=False, grade__gt=0, school_term__education=utils.EDUCATION_ARABIC).exists() and \
                    self.instance.level_ar.generic_level != level_ar.generic_level:
                    raise forms.ValidationError("Impossible de changer de niveau l'élève possède déjà des notes")
            elif not (self.instance.level_ar == level_ar) and level_ar.enrollment_set.count() >= level_ar.max:
                raise forms.ValidationError(f"Effectif maxi atteint: {level_ar.max}")
        return level_ar

    def __init__(self, user, year, pk=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['level_ar'].widget.attrs['hx-include'] = \
            "[name='2-status'], [name='2-level_fr'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees']"
        self.fields['level_fr'].widget.attrs['hx-include'] = \
            "[name='2-status'], [name='2-level_ar'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees']"
        self.fields['status'].widget.attrs['hx-include'] = \
            "[name='2-level_fr'], [name='2-level_ar'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees']"

        filter_levels_to_display(pk, self, user, year, kwargs)
        
        if not user.has_perm('school.add_payment'):
            self.fields['enrollment_fees'].widget.attrs['readOnly'] = True
            self.fields['year_fees'].widget.attrs['readOnly'] = True
            self.fields['annexe_fees'].widget.attrs['readOnly'] = True
            self.fields['enrollment_fee1'].widget.attrs['readOnly'] = True
            self.fields['year_fee1'].widget.attrs['readOnly'] = True
            self.fields['annexe_fee1'].widget.attrs['readOnly'] = True

class StudentFeesPartial(forms.ModelForm):
    prefix = '2'

    def __init__(self, create_mode, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['level_ar'].widget.attrs['hx-include'] = \
            "[name='2-status'], [name='2-level_fr'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees'], [name='2-generic_level_fr'], [name='2-generic_level_ar']"
        self.fields['level_fr'].widget.attrs['hx-include'] = \
            "[name='2-status'], [name='2-level_ar'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees'], [name='2-generic_level_fr'], [name='2-generic_level_ar']"
        self.fields['generic_level_ar'].widget.attrs['hx-include'] = \
            "[name='2-status'], [name='2-level_fr'], [name='2-level_ar'], [name='2-generic_level_fr'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees']"
        self.fields['generic_level_fr'].widget.attrs['hx-include'] = \
            "[name='2-status'], [name='2-level_ar'], [name='2-generic_level_ar'], [name='2-level_fr'], [name='2-enrollment_fees'], [name='2-year_fees'], [name='2-annexe_fees']"
        
        if create_mode:
            for field in self.fields:
                self.fields[field].widget.attrs['hx-vals'] = json.dumps({'create_mode': '1'})
    class Meta:
        model = models.Enrollment
        fields = [
            'enrollment_fees', 'year_fees', 
            'annexe_fees', 'level_fr', 'level_ar',
            'generic_level_fr', 'generic_level_ar',
        ]


class PaymentForm(forms.ModelForm):
    student_id = forms.CharField(label='Matricule ou identifiant')
    last_name = forms.CharField(max_length=255, label='Nom', 
                required=False)
    first_name = forms.CharField(max_length=255, label='Prénoms', 
                 required=False)
    level_fr = forms.ModelChoiceField(
        label='Classe', required=False,
        queryset=models.Level.objects.all())
    level_ar = forms.ModelChoiceField(
        label='Classe arabe', 
        queryset=models.Level.objects.all(), 
        required=False)
    enrollment_fee1 = forms.IntegerField(
        initial=0, required=False, label="Inscription")
    year_fee1 = forms.IntegerField(
        initial=0, required=False, label="Scolarité")
    annexe_fee1 = forms.IntegerField(
        initial=0, required=False, label="Annexe")
    date1 = forms.DateField(
        initial=timezone.now, required=False, label="Date")

    class Meta:
        model = models.Payment
        fields = [
            'student_id', 'last_name', 'first_name', 
            'payment_type', 'annexe_category'
        ]

    def __init__(self, user, student_id=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        queryset_fr = models.Level.objects.for_user(user, user.school)
        queryset_ar = models.Level.objects.for_user(
            user, user.school, education=utils.EDUCATION_ARABIC)
        self.fields['level_fr'].queryset = queryset_fr
        self.fields['level_ar'].queryset = queryset_ar

        # Make fields readOnly
        self.fields['last_name'].widget.attrs['readOnly'] = True
        self.fields['first_name'].widget.attrs['readOnly'] = True
        self.fields['level_fr'].widget.attrs['readOnly'] = True
        self.fields['level_ar'].widget.attrs['readOnly'] = True


class PricingForm(forms.ModelForm):
    class Meta:
        model = models.LevelPricing
        fields = [
            'education', 'generic_level', 'student_status', 
            'inscription', 'scolarite', 'annexe'
        ]


class CyclePricingForm(forms.ModelForm):
    PRICING_CYCLE_CHOICES = (
        (utils.CYCLE_PRIMARY, 'Primaire (Matlle -> CM2)'),
        ('1er', '1er Cycle (6ème -> 3ème)'),
        ('2nd', '2nd Cycle (2nde -> Tle)'),
    )
    cycle = forms.ChoiceField(choices=PRICING_CYCLE_CHOICES)

    class Meta:
        model = models.LevelPricing
        fields = [
            'education', 'cycle', 'student_status', 
            'inscription', 'scolarite', 'annexe'
        ]
    

class ExtraFeesForm(forms.ModelForm):
    class Meta:
        model = models.LevelExtraPrice
        fields = ['id', 'category', 'price_type', 'price']


ExtraFeesInlineFormSet = forms.inlineformset_factory(
            models.LevelPricing, models.LevelExtraPrice,
            form=ExtraFeesForm, extra=5)


class DateRangeForm(forms.Form):
    start = forms.DateField(required=True)
    end = forms.DateField(required=True)


class ImportForm(forms.Form):
    file = forms.FileField()
    active = forms.BooleanField(required=False, initial=False)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['file'].widget.attrs['accept'] = '.xls, .xlsx, .csv'


class LevelForm(forms.Form):
    level = forms.ModelChoiceField(
        queryset=None, required=True, 
        label='Classe')
    subschool = forms.ModelChoiceField(
        queryset=None, required=False,
        label='Ecole', help_text='Optionnel')
    
    def __init__(self, queryset=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['level'].queryset = queryset
        # Filter subschools based on the school
        if 'school' in kwargs:
            school = kwargs['school']
            if school.subschool_set.count() >= 2:
                self.fields['subschool'].queryset = school.subschool_set.all()
            else:
                del self.fields['subschool']


class PhotoInputForm(forms.ModelForm):
    class Meta:
        model = models.Student
        fields = ['photo']


class LevelSubjectsForm(forms.ModelForm):
    def __init__(self, user, level=None, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        qs = LevelSubject.objects.none()
        if level:
            qs = LevelSubject.objects.filter(
            level=level.generic_level,
            subject__education=level.education,
            year=level.year,
            school=user.school)
            qs = qs.filter(
                ~Q(teacherlevel2__level=level)
            )
        self.fields['subjects'].queryset = qs

    class Meta:
        model = models.TeacherLevel2
        fields = ['subjects']
    

class StudentsFilterForm(forms.ModelForm):
    choices = (('', 'Sexe'),)
    for item in utils.GENDER_CHOICES:
        choices += (item[0], item[0]),
    gender = forms.ChoiceField(choices=choices, initial='', label='Sexe')

    years = (('', 'Né en'),)
    current_year = date.today().year
    for i in range(current_year - 2, 1980, -1):
        years += (i, i),
    birth_year = forms.ChoiceField(choices=years, initial='', label='né en')

    def __init__(
        self, user, statut='', education='', 
        year=None, generic_level_ar='', generic_level_fr='', 
        birth_year=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        generic_levels = models.GenericLevel.objects \
            .for_school(user, year) \
            .only('short_name', 'order')

        for key, field in self.fields.items():
            attrs = f"?statut="

            if statut:
                attrs += f"{statut}"
            if education:
                attrs += f"&education={education}"
            field.widget.attrs['hx-get'] = attrs    
            field.empty_label = field.label

            if key == 'status' or key == 'qualite':
                field.initial = ''
            elif key == 'birth_year':
                field.initial = ''
            elif key.startswith('generic_'):
                field.queryset = generic_levels
            elif key.startswith('level_'):
                field.queryset = field.queryset.filter(year=year, school=user.school)

                if key == 'level_ar':
                    field.queryset = field.queryset.select_related('generic_level') \
                        .filter(education=utils.EDUCATION_ARABIC)
                if key == 'level_fr':
                    field.queryset = field.queryset.select_related('generic_level') \
                        .filter(education=utils.EDUCATION_FRENCH)
                if generic_level_ar and key == 'level_ar':
                    field.queryset = field.queryset.filter(generic_level__id=generic_level_ar)
                if generic_level_fr and key == 'level_fr':
                    field.queryset = field.queryset.filter(generic_level__id=generic_level_fr)

            # includes = '[name=search], [name=per_page], [name=gender]'
            # for i, inner_field in enumerate(self.fields):
            #     if inner_field != key and i != len(self.fields) - 1:
            #         includes += f'[name={inner_field}],'
            #     elif inner_field != key and i == len(self.fields) -1:
            #         includes += f'[name={inner_field}]'
            # if includes.endswith(','):
            #     includes = includes[:-1]
            # field.widget.attrs['hx-include'] = includes
            field.empty_label = 'Tous'
            if field.choices:
                field.choices = [('', 'Tous')] + \
                    list(field.choices)[1:]
    class Meta:
        model = models.Enrollment
        fields = [
            'generic_level_fr',  
            'generic_level_ar', 'status', 'qualite',
            'gender', 'birth_year', 'level_ar', 'level_fr'
        ]


class TestSchoolForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['cycle'].initial = utils.CYCLE_PRIMARY

    class Meta:
        model = models.School
        fields = [
            'name', 'translation', 'location', 
            'code', 'cycle', 'status', 'education',
            'association', 'IEP', 'secteur_p', 'logo',
            'education', 'phone1', 'phone2', 'address'
        ]


class SubscriptionForm(forms.ModelForm):
    admin = forms.CharField(max_length=255, required=True)
    admin_phone = forms.CharField(max_length=10, min_length=10, required=False)
    scientist = forms.CharField(max_length=255, required=True)
    scientist_phone = forms.CharField(max_length=10, min_length=10, required=True)
    sms_phone = forms.CharField(max_length=10, min_length=10, required=True)
    class Meta:
        model = models.Subscription
        fields = [
            'plan', 'level', 'admin', 'admin_phone', 'scientist',
            'scientist_phone', 'sms_phone'
        ]


class TestSchoolFormStep1(forms.ModelForm):
    education = forms.ChoiceField(choices=(
        (utils.EDUCATION_FRENCH, 'Laique'), (utils.EDUCATION_ARABIC, 'Islamique'), 
    ), label="Type d'école (islamique/laique)")
        
    
    class Meta:
        model = models.School
        fields = [
            'location', 'education', 'status', 
            'cycle', 'IEP', 'secteur_p',
        ]

class TestSchoolFormStep2(forms.ModelForm):
    class Meta:
        model = models.School
        fields = [
            'name', 'translation', 
            'code', 'phone1', 'phone2', 
            'email', 'address', 'logo'
        ]


class TestSchoolFormStep3(forms.ModelForm):
    class Meta:
        model = models.Subscription
        fields = ['plan', 'level']


class TestSchoolFormStep4(forms.ModelForm):
    # admin = forms.CharField(max_length=255, required=False, label='Fondateur')
    # admin_phone = forms.CharField(max_length=10, min_length=10, required=False, label='Contact fondateur')
    scientist = forms.CharField(max_length=255, required=True, label='Fondateur')
    scientist_phone = forms.CharField(max_length=10, min_length=10, required=True, label='Contact')
    sms_phone = forms.CharField(
        max_length=10, min_length=10, 
        required=True, label='Contact SMS',
        help_text='Contact sur lequel vous recevrez les informations de connexion'
    )

    class Meta:
        model = models.Subscription
        fields = [
            'scientist', 'scientist_phone', 'sms_phone'
        ]


class SettingsSchoolForm(forms.ModelForm):
    class Meta:
        model = models.School
        fields = [
            'location', 'exact_location', 'status',
            'name', 'name_secondary', 'translation',
            'IEP', 'secteur_p', 'address', 'logo',
            'email',
            'phone1', 'phone2'
        ]

class SettingsSchoolDirectionForm(forms.ModelForm):
    class Meta:
        model = models.School
        fields = [
            'director_fr', 'director_ar', 'director_secondary'
        ]

class SettingsSchoolHeaderForm(forms.ModelForm):
    class Meta:
        model = models.School
        fields = [
            'left_header', 'right_header'
        ]
        widgets = {
          'left_header': forms.Textarea(attrs={'rows': 6, 'cols': 35, 'style': 'text-align: center'}),
          'right_header': forms.Textarea(attrs={'rows': 6, 'cols': 35, 'style': 'text-align: center'}),
        }


class LevelCreationForm(forms.ModelForm):
    def __init__(self, user=None, year=None, *args, **kwargs):
        super(LevelCreationForm, self).__init__(*args, **kwargs)
        for generic_level in models.GenericLevel.objects.for_school(user=user, year=year).all():
            field_name = f'num_levels_{generic_level.id}'
            self.fields[field_name] = forms.IntegerField(
                label=str(generic_level),
                min_value=0,
                required=False,
                initial=0
            )
        # Add subschool choices if school has multiple subschools
        if user.school.subschool_set.count() >= 2:
            self.fields['subschool'].queryset = user.school.subschool_set.all()
        else:
            del self.fields['subschool']

    class Meta:
        model = models.Level
        fields = ['subschool']


class StaffForm(forms.ModelForm):
    class Meta:
        model = models.Staff
        exclude = ['school', 'user', 'teacher']


class PaymentDecisionForm(forms.ModelForm):
    class Meta:
        model = models.SalaryPaymentDecision
        fields = ['year', 'month', 'payment_date', 'payment_method', 'decision_number']
    
    # Let's dynamically create fields payment options fields
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        payment_options = models.SalaryPaymentOptions.objects.all()
        print(payment_options)
        for option in payment_options:
            if option.operation == models.SalaryPaymentOptions.OPERATION_ADDING:
                self.fields[f"option_add_{'rate' if option.rate else 'amount'}_{option.pk}"] = forms.IntegerField(
                    label=option.name, required=False, initial=option.rate or option.amount,
                    help_text='Taux (entre 1 et 100)' if option.rate else 'Montant',
                    max_value=100 if option.rate else None
                )
            else:
                self.fields[f"option_sub_{'rate' if option.rate else 'amount'}_{option.pk}"] = forms.IntegerField(
                    label=option.name, required=False, initial=option.rate or option.amount,
                    help_text='Taux (entre 1 et 100)' if option.rate else 'Montant',
                    max_value=100 if option.rate else None
                )

# Add this new form class
class ConfirmationForm(forms.Form):
    """Empty form for the confirmation step"""
    confirmation = forms.BooleanField(
        required=True, 
        initial=True, 
        label="J'ai vérifié les informations et je confirme la création de l'école",
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )