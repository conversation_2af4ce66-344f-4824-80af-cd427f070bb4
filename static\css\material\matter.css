.matter-textfield-outlined {
    --matter-helper-theme: rgb(var(--matter-theme-rgb, var(--matter-primary-rgb, 33, 150, 243)));
    --matter-helper-safari1: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.38);
    --matter-helper-safari2: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.6);
    --matter-helper-safari3: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.87);
    position: relative;
    display: inline-block;
    padding-top: 6px;
    font-family: var(--matter-font-family, "Roboto", "Segoe UI", BlinkMacSystemFont, system-ui, -apple-system);
    font-size: 16px;
    line-height: 1.5;
}

/* Input, Textarea */
.matter-textfield-outlined > input,
.matter-textfield-outlined > textarea {
    box-sizing: border-box;
    margin: 0;
    border-style: solid;
    border-width: 1px;
    border-color: transparent var(--matter-helper-safari2) var(--matter-helper-safari2);
    border-radius: 4px;
    padding: 15px 13px 15px;
    width: 100%;
    height: inherit;
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.87);
    -webkit-text-fill-color: currentColor; /* Safari */
    background-color: transparent;
    box-shadow: inset 1px 0 transparent, inset -1px 0 transparent, inset 0 -1px transparent;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    caret-color: var(--matter-helper-theme);
    transition: border 0.2s, box-shadow 0.2s;
    position: relative;
    z-index: 2; /* Ensure input is above the span */
}

.matter-textfield-outlined > input:not(:focus):placeholder-shown,
.matter-textfield-outlined > textarea:not(:focus):placeholder-shown {
    border-top-color: var(--matter-helper-safari2);
}

/* Span */
.matter-textfield-outlined > input + span,
.matter-textfield-outlined > textarea + span {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    width: 100%;
    max-height: 100%;
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.6);
    font-size: 75%;
    line-height: 15px;
    cursor: text;
    transition: color 0.2s, font-size 0.2s, line-height 0.2s;
    z-index: 1; /* Below the input */
    pointer-events: none; /* Allow clicks to pass through to input */
}

.matter-textfield-outlined > input:not(:focus):placeholder-shown + span,
.matter-textfield-outlined > textarea:not(:focus):placeholder-shown + span {
    font-size: inherit;
    line-height: 68px;
}

/* Corners */
.matter-textfield-outlined > input + span::before,
.matter-textfield-outlined > input + span::after,
.matter-textfield-outlined > textarea + span::before,
.matter-textfield-outlined > textarea + span::after {
    content: "";
    display: block;
    box-sizing: border-box;
    margin-top: 6px;
    border-top: solid 1px var(--matter-helper-safari2);
    min-width: 10px;
    height: 8px;
    pointer-events: none;
    box-shadow: inset 0 1px transparent;
    transition: border 0.2s, box-shadow 0.2s;
}

.matter-textfield-outlined > input + span::before,
.matter-textfield-outlined > textarea + span::before {
    margin-right: 4px;
    border-left: solid 1px transparent;
    border-radius: 4px 0;
}

.matter-textfield-outlined > input + span::after,
.matter-textfield-outlined > textarea + span::after {
    flex-grow: 1;
    margin-left: 4px;
    border-right: solid 1px transparent;
    border-radius: 0 4px;
}

.matter-textfield-outlined > input:not(:focus):placeholder-shown + span::before,
.matter-textfield-outlined > textarea:not(:focus):placeholder-shown + span::before,
.matter-textfield-outlined > input:not(:focus):placeholder-shown + span::after,
.matter-textfield-outlined > textarea:not(:focus):placeholder-shown + span::after {
    border-top-color: transparent;
}

/* Hover */
.matter-textfield-outlined:hover > input,
.matter-textfield-outlined:hover > textarea {
    border-color: transparent var(--matter-helper-safari3) var(--matter-helper-safari3);
}

.matter-textfield-outlined:hover > input + span::before,
.matter-textfield-outlined:hover > textarea + span::before,
.matter-textfield-outlined:hover > input + span::after,
.matter-textfield-outlined:hover > textarea + span::after {
    border-top-color: var(--matter-helper-safari3);
}

.matter-textfield-outlined:hover > input:not(:focus):placeholder-shown,
.matter-textfield-outlined:hover > textarea:not(:focus):placeholder-shown {
    border-color: var(--matter-helper-safari3);
}

/* Focus */
.matter-textfield-outlined > input:focus,
.matter-textfield-outlined > textarea:focus {
    border-color: transparent var(--matter-helper-theme) var(--matter-helper-theme);
    box-shadow: inset 1px 0 var(--matter-helper-theme), inset -1px 0 var(--matter-helper-theme), inset 0 -1px var(--matter-helper-theme);
    outline: none;
}

.matter-textfield-outlined > input:focus + span,
.matter-textfield-outlined > textarea:focus + span {
    color: var(--matter-helper-theme);
}

.matter-textfield-outlined > input:focus + span::before,
.matter-textfield-outlined > input:focus + span::after,
.matter-textfield-outlined > textarea:focus + span::before,
.matter-textfield-outlined > textarea:focus + span::after {
    border-top-color: var(--matter-helper-theme) !important;
    box-shadow: inset 0 1px var(--matter-helper-theme);
}

/* Disabled */
.matter-textfield-outlined > input:disabled,
.matter-textfield-outlined > input:disabled + span,
.matter-textfield-outlined > textarea:disabled,
.matter-textfield-outlined > textarea:disabled + span {
    border-color: transparent var(--matter-helper-safari1) var(--matter-helper-safari1) !important;
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.38);
    pointer-events: none;
}

.matter-textfield-outlined > input:disabled + span::before,
.matter-textfield-outlined > input:disabled + span::after,
.matter-textfield-outlined > textarea:disabled + span::before,
.matter-textfield-outlined > textarea:disabled + span::after {
    border-top-color: var(--matter-helper-safari1) !important;
}

.matter-textfield-outlined > input:disabled:placeholder-shown,
.matter-textfield-outlined > input:disabled:placeholder-shown + span,
.matter-textfield-outlined > textarea:disabled:placeholder-shown,
.matter-textfield-outlined > textarea:disabled:placeholder-shown + span {
    border-top-color: var(--matter-helper-safari1) !important;
}

.matter-textfield-outlined > input:disabled:placeholder-shown + span::before,
.matter-textfield-outlined > input:disabled:placeholder-shown + span::after,
.matter-textfield-outlined > textarea:disabled:placeholder-shown + span::before,
.matter-textfield-outlined > textarea:disabled:placeholder-shown + span::after {
    border-top-color: transparent !important;
}

/* Faster transition in Safari for less noticable fractional font-size issue */
@media not all and (min-resolution:.001dpcm) {
    @supports (-webkit-appearance:none) {
        .matter-textfield-outlined > input,
        .matter-textfield-outlined > input + span,
        .matter-textfield-outlined > textarea,
        .matter-textfield-outlined > textarea + span,
        .matter-textfield-outlined > input + span::before,
        .matter-textfield-outlined > input + span::after,
        .matter-textfield-outlined > textarea + span::before,
        .matter-textfield-outlined > textarea + span::after {
            transition-duration: 0.1s;
        }
    }
}


.matter-textfield-filled {
    --matter-helper-theme: var(--matter-theme-rgb, var(--matter-primary-rgb, 33, 150, 243));
    position: relative;
    display: inline-block;
    font-family: var(--matter-font-family, "Roboto", "Segoe UI", BlinkMacSystemFont, system-ui, -apple-system);
    font-size: 16px;
    line-height: 1.5;
}

/* Input, Textarea */
.matter-textfield-filled > input,
.matter-textfield-filled > textarea {
    display: block;
    box-sizing: border-box;
    margin: 0;
    border: none;
    border-top: solid 24px transparent;
    border-bottom: solid 1px rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.6);
    border-radius: 4px 4px 0 0;
    padding: 0 12px 7px;
    width: 100%;
    height: inherit;
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.87);
    -webkit-text-fill-color: currentColor; /* Safari */
    background-color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.04);
    box-shadow: none; /* Firefox */
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    caret-color: rgb(var(--matter-helper-theme));
    transition: border-bottom 0.2s, background-color 0.2s;
}

/* Span */
.matter-textfield-filled > input + span,
.matter-textfield-filled > textarea + span {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: block;
    box-sizing: border-box;
    padding: 7px 12px 0;
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.6);
    font-size: 75%;
    line-height: 18px;
    pointer-events: none;
    transition: color 0.2s, font-size 0.2s, line-height 0.2s;
}

/* Underline */
.matter-textfield-filled > input + span::after,
.matter-textfield-filled > textarea + span::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    display: block;
    width: 100%;
    height: 2px;
    background-color: rgb(var(--matter-helper-theme));
    transform-origin: bottom center;
    transform: scaleX(0);
    transition: transform 0.3s;
}

/* Hover */
.matter-textfield-filled:hover > input,
.matter-textfield-filled:hover > textarea {
    border-bottom-color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.87);
    background-color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.08);
}

/* Placeholder-shown */
.matter-textfield-filled > input:not(:focus):placeholder-shown + span,
.matter-textfield-filled > textarea:not(:focus):placeholder-shown + span {
    font-size: inherit;
    line-height: 48px;
}

/* Focus */
.matter-textfield-filled > input:focus,
.matter-textfield-filled > textarea:focus {
    outline: none;
}

.matter-textfield-filled > input:focus + span,
.matter-textfield-filled > textarea:focus + span {
    color: rgb(var(--matter-helper-theme));
}

.matter-textfield-filled > input:focus + span::after,
.matter-textfield-filled > textarea:focus + span::after {
    transform: scale(1);
}

/* Disabled */
.matter-textfield-filled > input:disabled,
.matter-textfield-filled > textarea:disabled {
    border-bottom-color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.38);
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.38);
    background-color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.24);
}

.matter-textfield-filled > input:disabled + span,
.matter-textfield-filled > textarea:disabled + span {
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.38);
}

/* Faster transition in Safari for less noticable fractional font-size issue */
@media not all and (min-resolution:.001dpcm) {
    @supports (-webkit-appearance:none) {
        .matter-textfield-filled > input,
        .matter-textfield-filled > input + span,
        .matter-textfield-filled > input + span::after,
        .matter-textfield-filled > textarea,
        .matter-textfield-filled > textarea + span,
        .matter-textfield-filled > textarea + span::after {
            transition-duration: 0.1s;
        }
    }
}

/* ========================================
   MATTER SELECT STYLES
   ======================================== */

.matter-select-wrapper {
    position: relative;
    width: 100%;
    margin: 8px 0;
    font-family: var(--matter-font-family, "Roboto", "Segoe UI", BlinkMacSystemFont, system-ui, -apple-system);
    font-size: 16px;
    line-height: 1.5;
}

.matter-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 100%;
    border: 1px solid rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.6);
    padding: 15px 40px 15px 13px;
    font-size: 16px;
    border-radius: 4px;
    background: transparent;
    outline: none;
    z-index: 1;
    cursor: pointer;
    font-family: inherit;
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.87);
    transition: border 0.2s ease;
}

.matter-select-wrapper::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 12px;
    border: 6px solid transparent;
    border-top-color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.6);
    transform: translateY(-50%) rotate(0deg);
    transition: transform 0.2s ease;
    pointer-events: none;
}

/* Rotate the arrow when select is open */
.matter-select-wrapper[data-open="true"]::after {
    transform: translateY(-50%) rotate(180deg);
}

.matter-select-label {
    position: absolute;
    left: 13px;
    top: 15px;
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.6);
    font-size: 16px;
    background: white;
    padding: 0 4px;
    transition: all 0.2s ease;
    pointer-events: none;
    font-family: inherit;
}

.matter-select:focus + .matter-select-label,
.matter-select:not([value=""]):valid + .matter-select-label,
.matter-select.has-value + .matter-select-label {
    top: -8px;
    left: 9px;
    font-size: 12px;
    color: rgb(var(--matter-theme-rgb, var(--matter-primary-rgb, 33, 150, 243)));
}

.matter-select:focus {
    border: 2px solid rgb(var(--matter-theme-rgb, var(--matter-primary-rgb, 33, 150, 243)));
    padding: 14px 39px 14px 12px; /* Adjust padding to account for thicker border */
}

.matter-select:focus + .matter-select-label {
    color: rgb(var(--matter-theme-rgb, var(--matter-primary-rgb, 33, 150, 243)));
}

/* Error states */
.matter-select.error {
    border-color: #f44336;
}

.matter-select.error + .matter-select-label {
    color: #f44336;
}

/* Disabled state */
.matter-select:disabled {
    background-color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.04);
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.38);
    cursor: not-allowed;
    border-color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.38);
}

.matter-select:disabled + .matter-select-label {
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.38);
    background: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.04);
}

/* ========================================
   MATTER FORM ERROR STYLES
   ======================================== */

.matter-field-errors {
    margin-top: 4px;
}

.matter-error-text {
    color: #f44336;
    font-size: 12px;
    font-family: var(--matter-font-family, "Roboto", "Segoe UI", BlinkMacSystemFont, system-ui, -apple-system);
    margin-bottom: 2px;
}

/* Error states for text fields */
.matter-textfield-outlined.error > input,
.matter-textfield-outlined.error > textarea {
    border-color: #f44336;
    caret-color: #f44336;
}

.matter-textfield-outlined.error > input + span,
.matter-textfield-outlined.error > textarea + span {
    color: #f44336;
}

.matter-textfield-outlined.error > input:focus + span,
.matter-textfield-outlined.error > textarea:focus + span {
    color: #f44336;
}

/* ========================================
   MATTER SMALL VARIANTS
   ======================================== */

/* Small text field variant */
.matter-textfield-outlined.small {
    font-size: 14px;
    padding-top: 4px;
}

.matter-textfield-outlined.small > input,
.matter-textfield-outlined.small > textarea {
    padding: 12px 10px 12px;
    font-size: 14px;
}

/* Small variant label positioning - when not focused and empty */
.matter-textfield-outlined.small > input:not(:focus):placeholder-shown + span,
.matter-textfield-outlined.small > textarea:not(:focus):placeholder-shown + span {
    font-size: 14px;
    line-height: 52px; /* Move label slightly down for better centering */
}

/* Small variant label positioning - when focused or has value */
.matter-textfield-outlined.small > input:focus + span,
.matter-textfield-outlined.small > input:not(:placeholder-shown) + span,
.matter-textfield-outlined.small > textarea:focus + span,
.matter-textfield-outlined.small > textarea:not(:placeholder-shown) + span {
    font-size: 11px;
    line-height: 15px;
}

/* Small variant border corners adjustment */
.matter-textfield-outlined.small > input + span::before,
.matter-textfield-outlined.small > input + span::after,
.matter-textfield-outlined.small > textarea + span::before,
.matter-textfield-outlined.small > textarea + span::after {
    margin-top: 4px; /* Smaller margin for compact design */
    height: 6px; /* Smaller height */
}

/* Small select variant */
.matter-select-wrapper.small {
    font-size: 14px;
    margin: 6px 0;
}

.matter-select-wrapper.small .matter-select {
    padding: 12px 35px 12px 10px;
    font-size: 14px;
}

.matter-select-wrapper.small .matter-select-label {
    left: 10px;
    top: 12px;
    font-size: 14px;
}

.matter-select-wrapper.small .matter-select:focus + .matter-select-label,
.matter-select-wrapper.small .matter-select:not([value=""]):valid + .matter-select-label,
.matter-select-wrapper.small .matter-select.has-value + .matter-select-label {
    top: -6px;
    left: 6px;
    font-size: 11px;
}

.matter-select-wrapper.small .matter-select:focus {
    padding: 11px 34px 11px 9px; /* Adjust for thicker border */
}

.matter-select-wrapper.small::after {
    right: 10px;
    border-width: 5px;
}

/* ========================================
   MATTER SELECT STYLES
   ======================================== */

.matter-select-wrapper {
    position: relative;
    width: 100%;
    margin: 8px 0;
    font-family: var(--matter-font-family, "Roboto", "Segoe UI", BlinkMacSystemFont, system-ui, -apple-system);
    font-size: 16px;
    line-height: 1.5;
}

.matter-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 100%;
    border: 1px solid rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.6);
    padding: 15px 40px 15px 13px;
    font-size: 16px;
    border-radius: 4px;
    background: transparent;
    outline: none;
    z-index: 1;
    cursor: pointer;
    font-family: inherit;
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.87);
    transition: border 0.2s ease;
}

.matter-select-wrapper::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 12px;
    border: 6px solid transparent;
    border-top-color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.6);
    transform: translateY(-50%) rotate(0deg);
    transition: transform 0.2s ease;
    pointer-events: none;
}

/* Rotate the arrow when select is open */
.matter-select-wrapper[data-open="true"]::after {
    transform: translateY(-50%) rotate(180deg);
}

.matter-select-label {
    position: absolute;
    left: 13px;
    top: 15px;
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.6);
    font-size: 16px;
    background: white;
    padding: 0 4px;
    transition: all 0.2s ease;
    pointer-events: none;
    font-family: inherit;
}

.matter-select:focus + .matter-select-label,
.matter-select:not([value=""]):valid + .matter-select-label,
.matter-select.has-value + .matter-select-label {
    top: -8px;
    left: 9px;
    font-size: 12px;
    color: rgb(var(--matter-theme-rgb, var(--matter-primary-rgb, 33, 150, 243)));
}

.matter-select:focus {
    border: 2px solid rgb(var(--matter-theme-rgb, var(--matter-primary-rgb, 33, 150, 243)));
    padding: 14px 39px 14px 12px; /* Adjust padding to account for thicker border */
}

.matter-select:focus + .matter-select-label {
    color: rgb(var(--matter-theme-rgb, var(--matter-primary-rgb, 33, 150, 243)));
}

/* Error states */
.matter-select.error {
    border-color: #f44336;
}

.matter-select.error + .matter-select-label {
    color: #f44336;
}

/* Disabled state */
.matter-select:disabled {
    background-color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.04);
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.38);
    cursor: not-allowed;
    border-color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.38);
}

.matter-select:disabled + .matter-select-label {
    color: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.38);
}

.matter-select:disabled + .matter-select-label {
    background: rgba(var(--matter-onsurface-rgb, 0, 0, 0), 0.04);
}



/* Utilities */
.d-none {
    display: none;
}